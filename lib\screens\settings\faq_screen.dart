/// 바라 부스 매니저 - 자주 묻는 질문 페이지
///
/// 사용자들이 자주 묻는 질문들을 탭별로 구분하여 제공하는 페이지입니다.
/// - 기능 사용법
/// - 결제 및 환불
/// - 오류 및 문제 해결
/// - 고객 지원
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월

import 'package:flutter/material.dart';
import '../../utils/app_colors.dart';
import '../../widgets/app_bar_styles.dart';

class FAQScreen extends StatefulWidget {
  const FAQScreen({super.key});

  @override
  State<FAQScreen> createState() => _FAQScreenState();
}

class _FAQScreenState extends State<FAQScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('자주 묻는 질문', style: AppBarStyles.of(context)),
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 탭 바 (앱바와 분리된 스타일)
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: AppColors.primarySeed,
                unselectedLabelColor: Colors.grey.shade600,
                indicatorColor: AppColors.primarySeed,
                indicatorWeight: 3,
                indicatorSize: TabBarIndicatorSize.label,
                labelStyle: const TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontFamily: 'Pretendard',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                tabs: const [
                  Tab(text: '앱 기능'),
                  Tab(text: '결제/환불'),
                  Tab(text: '오류 해결'),
                  Tab(text: '고객 지원'),
                ],
              ),
            ),
            // 탭 뷰
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildFeatureTab(),
                  _buildPaymentTab(),
                  _buildTroubleshootingTab(),
                  _buildSupportTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 기능 사용법 탭
  Widget _buildFeatureTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildFAQSection('상품 관리', [
          _buildFAQItem(
            '상품을 등록하려면 어떻게 하나요?',
            '개별 상품 등록:\n'
            '1. POS 화면 우측 상단의 연필 버튼 터치\n'
            '2. 각 카테고리에서 "+" 버튼 터치\n'
            '3. 상품명, 가격, 수량, 판매자 입력\n'
            '4. 필요시 상품 이미지 추가\n'
            '5. "저장" 버튼으로 완료\n\n'
            '상품명은 카테고리 내에서 중복될 수 없습니다.',
          ),
          _buildFAQItem(
            '대량의 상품을 등록하는 방법은 없나요?',
            '대량 상품 등록:\n'
            '1. POS 화면 우측 상단의 연필 버튼 터치\n'
            '2. 우측 상단의 톱니바퀴 버튼을 터치해 "대량 상품 등록" 선택\n'
            '3. 여러 상품을 한 번에 입력\n'
            '4. 일괄 입력 기능으로 가격/수량/판매자 한번에 설정\n'
            '5. "등록" 버튼으로 완료\n',
          ),
          _buildFAQItem(
            '상품 삭제는 어떻게 하나요?',
            '상품 삭제:\n'
            '1. POS 화면 우측 상단의 연필 버튼 터치\n'
            '2. 우측 하단에 나온 빨간 휴지통 버튼 터치\n'
            '3. 삭제할 상품들을 체크\n'
            '4. 다시 우측 하단의 빨간 휴지동 버튼을 터치로 완료. 취소하려면 X 버튼 터치\n'
            '5. "등록" 버튼으로 완료\n',
          ),
          _buildFAQItem(
            '카테고리는 뭐고, 어떻게 관리하나요?',
            '카테고리란 상품의 종류를 구분하는 기능입니다.\n'
            '카테고리 별로 POS에서 상품 위치가 구별되며,상품명을 표기할 때 자동으로 (카테고리명-상품명)으로 표시됩니다.\n\n'
            '카테고리 관리:\n'
            '1. POS 화면 우측 상단의 연필 버튼 터치\n'
            '2. 우측 상단의 톱니바퀴 버튼을 터치해 "카테고리 관리" 선택\n'
            '3. "카테고리 추가" 선택\n'
            '4. 카테고리 이름과, 구별할 색상을 선택\n'
            '5. "추가" 버튼 후 카테고리의 순서를 취향에 맞게 조정\n'
            '5. 우측 상단의 "체크" 버튼을 눌러 저장\n\n'
            '카테고리를 삭제하면 내부의 상품이 전부 삭제되니 주의하세요.',
          ),
          _buildFAQItem(
            '각 상품의 크기 조절은 가능 한가요?',
            '네 가능합니다.\n'
            '각 행별 열 수 조정으로 보이는 상품의 개수와 크기를 조절 할 수 있습니다.\n\n'
            '카테고리 관리:\n'
            '1. POS 화면 우측 상단의 연필 버튼 터치\n'
            '2. 우측 상단의 톱니바퀴 버튼을 터치해 "UI 열 수 조정" 선택\n'
            '3. 세로모드, 가로모드에 맞는 열 수 커스터마이징\n'
            '4. 확인 버튼을 눌러 완료.\n',
          ),

        ], sectionIcon: Icons.inventory_2),
        _buildFAQSection('판매 진행', [
          _buildFAQItem(
            '판매는 어떻게 진행하나요?',
            'POS 화면에서 판매 진행:\n'
            '1. 하단 중앙의 POS 버튼 터치\n'
            '2. 판매할 상품들을 터치하여 선택\n'
            '3. 우측 주문 패널에서 수량 조정(수량 영역 슬라이드, 터치해서 직접 입력)\n'
            '4. 결제 방법 선택 (현금/계좌이체/카드)\n'
            '5. "결제" 버튼으로 판매 완료\n\n'
            '판매와 동시에 재고가 자동 차감됩니다.',
          ),
          _buildFAQItem(
            '할인이나 서비스는 어떻게 제공하나요?',
            '할인 제공:\n'
            '1. 주문 패널 우측 상단의 "할인하기" 버튼 터치\n'
            '2. 할인할 금액을 입력 후 판매\n\n'
            '서비스 제공:\n'
            '1.POS 화면에서 상품을 길게 터치\n'
            '2.주문 패널에서 서비스 수량 조절(수량 영역 슬라이드, 터치해서 직접 입력)\n\n'
            '서비스는 금액은 계산하지 않지만, 재고가 차감됩니다.',
          ),
          _buildFAQItem(
            '세트할인은 뭐고, 어떻게 사용하나요?',
            '세트할인은, 특정한 조합이나 조건에 맞춰졌을 때 자동으로 금액이 할인되는 기능입니다. 플러스 이상 플랜에서 사용 가능합니다.\n'
            '상품 조합할인: 특정 상품들을 동시에 판매 시 할인이 적용됩니다.\n'
            '최소구매금액: 일정 금액 이상 구매 시 할인이 적용됩니다.\n'
            '카테고리별 수량할인: 특정 카테고리에서 일정 개수 이상 구매 시 할인이 적용됩니다.\n'
            '지정상품중 수량할인: 지정한 상품들 중에서 일정 개수 이상 구매 시 할인이 적용됩니다.\n\n'
            '세트할인 관리:\n'
            '1. POS 화면 우측 상단의 연필 버튼 터치\n'
            '2. 우측 상단의 톱니바퀴 버튼을 터치해 "세트할인 관리" 선택\n'
            '3. "세트할인 추가" 선택\n'
            '4. 세트할인 조건과, 세트 이름, 할인 금액을 입력\n'
            '5. 우측 상단의 체크 버튼을 눌러 저장\n'
            '5. 확인 후 우측 상단의 체크 버튼을 눌러 최종 저장\n\n'
            '이후 조건에 맞게 상품이 구성되면 자동으로 할인이 적용됩니다.',
          ),
          _buildFAQItem(
            '판매 내역은 어디서 확인하나요?',
            '"기록 및 통계" 탭의 "판매기록" 페이지에서 확인 가능합니다.\n',
          ),
        ], sectionIcon: Icons.attach_money),
        _buildFAQSection('선입금 관리', [
          _buildFAQItem(
            '선입금이란 무엇인가요?',
            '행사 전에 미리 받는 예약 결제입니다. 각종 플랫폼 중 "위치폼"을 기준으로 하고 있습니다.\n'
            '• 고객이 미리 상품을 예약하고 결제\n'
            '• 행사 당일 상품 수령\n'
            '• 재고 관리와 매출 예측에 도움\n\n'
            '특히 인기 상품의 사전 판매에 유용합니다.',
          ),
          _buildFAQItem(
            '선입금은 어떻게 등록하나요?',
            '1. "선입금" 탭 선택\n'
            '2. 우측 상단 "+" 버튼으로 새 선입금 등록\n'
            '3. 구매자 정보 입력 (이름, 연락처, 금액)\n'
            '4. 상품과 수량 선택\n'
            '5. 수령 예정 요일 설정\n\n'
            '이메일, 트위터계정, 환불 계좌, 메모등도 추가로 입력 가능합니다.',
          ),
          _buildFAQItem(
            '선입금 시스템은 어떻게 사용 가능한가요?',
            '선입금 수령 관리:\n\n'
            '수령 완료 처리:\n'
            '• 선입금 목록에서 해당 항목의 "수령" 버튼 터치\n'
            '• 버튼이 회색으로 변경되어 수령 완료 표시\n\n'
            '수령 취소:\n'
            '• 수령 완료된 항목의 버튼을 다시 터치\n'
            '• 다시 초록색 "수령" 버튼으로 변경\n\n'
            '선입금 수정/삭제:\n'
            '• 선입금 항목을 터치하여 상세 정보 확인\n'
            '• "수정" 또는 "삭제" 버튼 사용\n\n'
            '선입금 검색 및 필터링:\n'
            '• 좌측 상단의 돋보기 버튼을 눌러서 검색\n'
            '• 우측 상단의 정렬 버튼을 눌러서 필터링 및 정렬\n\n'
            'QR코드:\n'
            '• 우측 상단의 QR코드 버튼을 눌러서 촬영\n'
            '• 앱을 나가지 않아도 간편하게 바로 QR 확인을 할 수 있습니다.',         
          ),
          _buildFAQItem(
            '엑셀로 선입금 등록은 어떤 기능인가요?',
            '"위치폼"에서 받은 엑셀 파일로 간편하게 많은 선입금 데이터를 등록 가능한 서비스입니다. 플러스 플랜에서 사용 가능합니다.\n\n'
            '사용 방법:\n'
            '1. 위치폼에서 판매 폼 관리 > 해당 폼 선택 > 판매 내역 관리 > 엑셀 다운로드\n'
            '2. 선입금 탭의 우측 상단 "+" 버튼에서 "엑셀로 선입금 등록" 선택\n'
            '3. 다운로드 받은 엑셀 파일 선택\n'
            '4. 추출된 데이터 확인 후 "선입금 등록"을 터치하면 등록이 완료됩니다.\n\n'
            '자동 요일 수집을 원하신다면 추가 절차가 필요합니다.',
          ),
          _buildFAQItem(
            '엑셀로 자동으로 요일 수집은 어떻게 하나요?',
            '미리 폼을 등록 할 때 "추가 질문 받기" 에 수령 요일 예정을 추가해서 답변을 받아주셔야 합니다.\n'
            '답변은 무조건 N요일 로 답하도록 질문을 써 주세요. 문장중에 있더라도 N요일로 기재되었으면 자동 수집이 가능합니다.\n'
            '토, 일: X / 토요일, 일요일: O / 토요일에 갈거같아요 : O\n\n'
            '사용 방법:\n'
            '1. 선입금 엑셀 등록 페이지에서 우측 상단의 "달력" 버튼을 터치\n'
            '2. 엑셀에서 요일 수집 기능을 활성화\n'
            '3. 엑셀 파일을 확인해서 요일 질문이 있는 열을 입력 (예:AL)\n'
            '4. 엑셀 파일 선택 후 추출을 진행.\n\n'
            '요일 수집 답변이 없으면 날짜 설정은 수동으로만 가능합니다!',
          ),
          _buildFAQItem(
            '선입금 상품 연동은 무슨 기능인가요?',
            '실제 등록된 상품과 선입금 등록 시 사용했던 가상 상품을 연결해서 선입금 수령처리에 따라 실제 재고도 차감/복구 되는 기능입니다.\n\n'
            '사용 방법:\n'
            '1. 선입금 탭의 "상품연동 페이지"로 이동\n'
            '2. 우측 상단 "설정"버튼을 눌러 선입금 설정에 들어가 "재고연동" 활성화\n'
            '3. 연동시킬 선입금 가상 상품과 실제 상품을 체크해서 상단의 연동 버튼을 터치\n'
            '4. 선입금 수령 처리 시 실제 재고도 차감됩니다.\n\n'
            '우측 상단의 "목록" 버튼을 누르면, 링크되어있는 목록을 간편하게 볼 수 있습니다.\n'
            '테스트 개발중인 기능입니다. 많은 피드백 바랍니다.',
          ),
        ],sectionIcon: Icons.credit_card),
        _buildFAQSection('홈 대시보드', [
          _buildFAQItem(
            '홈 대시보드에 구성된 카드들은 어떤 기능인가요?',
            '홈 화면 주요 기능들:\n\n'
            '행사 표시:\n'
            '• 현재 행사 정보 표시, 터치시 행사 관리 페이지로 이동\n\n'
            '목표 수익 추적:\n'
            '• 날짜별 목표 수익 설정 및 진행도 확인 가능\n'
            '• 플러스 플랜에서는 판매자별 목표 수익 설정 가능\n\n'
            '판매 통계:\n'
            '• 간단한 판매 통계 요약 제공\n\n'
            '선입금 통계:\n'
            '• 선입금 수령 현황 수치 정보를 %로 제공\n\n'
            '체크리스트:\n'
            '• 터치 시 체크리스트 페이지로 이동\n'
            '• 한 행사에서 저장한 체크리스트는 다른 행사에도 목록 동기화\n'
            '• 체크리스트 추가 및 체크 기능 제공\n\n'
            '판매자 관리(플러스플랜 전용):\n'
            '• 터치 시 판매자 관리 페이지로 이동\n'
            '• 상품을 판매자별로 관리 할 수 있고, 판매자별 통계를 볼 수 있음',
          ),
          _buildFAQItem(
            '홈 대시보드 구성을 바꿀 수 있나요?',
            '아니요. 아쉽게도 홈 대시보드의 구성은 바꿀 수 없습니다.\n',
          ),
        ], sectionIcon: Icons.store),
        _buildFAQSection('행사 관리 시스템', [
          _buildFAQItem(
            '행사 관리 시스템이 뭔가요?',
            '기본적인 설정을 제외한, 모든 행사 데이터들을 따로 관리 할 수 있는 시스템입니다.\n'
            '프리 플랜에서는 한가지 행사만 관리 가능하며, 유료 플랜에서는 무제한으로 생성 할 수 있습니다.\n\n'
            '새 행사 등록하는 법 :\n\n'
            '1. 홈 대시보드에서 행사 카드를 터치해 행사 관리 페이지로 이동\n'
            '2. 우측 하단의 + 버튼을 입력해 새 행사를 등록 페이지로 이동\n'
            '3. 행사 이름, 행사 기간을 선택해 "생성" 버튼을 누르면 새 행사가 등록됩니다.\n\n'
            '등록된 행사를 터치하면 새로운 행사 관리 시스템으로 이동합니다.',
          ),
          _buildFAQItem(
            '행사는 어떻게 삭제하나요?',
            '행사가 1개만 남아있는 경우 행사를 삭제 할 수 없습니다.\n'
            '행사가 다수 존재하는 경우, 행사 관리 페이지에서 각 행사의 우측 점 세개 버튼을 눌러 삭제할 수 있습니다.\n'
            '프리 플랜에 경우, 행사 삭제 대신 행사 리셋 기능을 제공합니다.\n',
          ),
          _buildFAQItem(
            '다른 사람과 행사를 공유할 수 있나요?',
            '현재 바라 부스 매니저는 개인 사용자를 위한 로컬 전용 앱입니다.\n\n'
            '각 사용자는 자신의 디바이스에서 독립적으로 행사를 관리하며, 다른 사용자와 데이터를 공유하는 기능은 제공하지 않습니다.\n\n'
            '이는 개인정보 보호와 데이터 보안을 위한 설계입니다.',
          ),
        ], sectionIcon: Icons.add_shopping_cart),
        _buildFAQSection('통계 및 분석', [
          _buildFAQItem(
            '어떤 데이터의 통계를 제공 하고 있나요?',
            '간단한 무료 통계 데이터와, 프리미엄 유료 통계 데이터로 구분되어 있습니다.\n\n'
            '무료 통계 데이터:\n'
            '• 핵심지표 요약 (총매출/총거래수/평균거래액/총판매량)\n'
            '• 선입금 수령 현황 및 금액\n'
            '• 상품 별 매출 현황\n\n'
            '프리미엄 통계 데이터:\n'
            '• 카테고리별 판매 데이터 (그래프)\n'
            '• 시간대별 매출 (그래프)\n'
            '• 세트 판매 분석\n'
            '• 서비스 제공 현황\n'
            '• 판매자별 판매 통계(플러스플랜 전용)',
          ),
          _buildFAQItem(
            '통계 데이터를 파일로 내보낼 수 있나요?',
            '네, 통계 데이터를 파일로 내보내는 기능을 제공하고 있습니다.\n\n'
            '플러스 플랜:\n'
            '• Excel (.xlsx)\n\n'
            '• 그래프를 포함한 상세 PDF 보고서\n\n'
            '내보내기 방법:\n'
            '1. 통계 페이지의 우측 상단 "다운로드" 버튼을 터치\n'
            '2. 내보낼 파일의 양식을 선택\n'
            '3. 미리보기 확인 후 다운로드나 공유 버튼을 눌러 내보내기',

          ),
        ], sectionIcon: Icons.bar_chart),
        _buildFAQSection('데이터 관리', [
          _buildFAQItem(
            '데이터는 어떻게 저장되나요?',
            '플랜별 백업 방식:\n\n'
            '플러스 플랜:\n'
            '• 구글 드라이브 백업 및 복구 기능 제공\n'
            '• 여러 기기에서 동일한 데이터 접근 가능\n\n'
            '프리 플랜:\n'
            '• 로컬 저장만 지원\n'
            '• 앱 삭제 시 행사 데이터가 전부 삭제됩니다.',
          ),
        ], sectionIcon: Icons.backup),
      ],
    );
  }

  /// 결제/환불 탭
  Widget _buildPaymentTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildFAQSection('구독 및 결제', [
          _buildFAQItem(
            '플랜별 차이점이 무엇인가요?',
            '무료 플랜:\n'
            '• 행사 1개 제한\n'
            '• 상품 30개 제한\n'
            '• 로컬 저장만 가능\n'
            '• 기본 POS 사용 가능\n'
            '• 목표수익, 체크리스트, 선입금 관리 및 연동\n\n'
            '플러스 플랜 (월 3,500원):\n'
            '• 무제한 행사 및 상품\n'
            '• 세트 할인 기능\n'
            '• 서비스 기능\n'
            '• 엑셀로 선입금 일괄 등록\n'
            '• 엑셀 내보내기\n'
            '• 고급 통계 분석\n'
            '• 로컬 저장만 (클라우드 동기화 불가)\n\n'

            '자세한 내용은 플랜 비교 페이지에서 확인해 주세요.',
          ),
          _buildFAQItem(
            '결제는 언제 이루어지나요?',
            '구독 결제 일정:\n'
            '• 첫 결제: 구독 즉시\n'
            '• 정기 결제: 매월 구독일에 자동 결제\n\n'
            '플레이스토어/앱스토어를 통해 자동 결제됩니다.\n'
            '결제 실패 시 고객센터 메일로 문의 부탁드립니다.',
          ),
        ], sectionIcon: Icons.payment),
        _buildFAQSection('구독 관리', [
          _buildFAQItem(
            '구독을 취소하려면?',
            '플레이스토어나, 앱스토어에서 구독을 취소해주세요.\n\n'
            '중요사항:\n'
            '• 즉시 취소되지 않습니다\n'
            '• 다음 결제일까지 현재 플랜 유지\n'
            '• 다음 결제일 이후 무료 플랜으로 전환\n'
            '• 이미 결제된 금액은 환불되지 않습니다',
          ),
          _buildFAQItem(
            '환불은 어떻게 받나요?',
            '환불 절차:\n'
            '1. 먼저 구독 취소 진행 (My 페이지 > 구독 관리)\n'
            '2. 고객센터 이메일로 환불 요청\n'
            '3. 다음 정보 포함 필수:\n'
            '   - 등록된 이메일 주소\n'
            '   - 환불 사유\n'
            '   - 결제 영수증 (스크린샷)\n\n'
            '중요: 환불 진행 시 현재 활성화된 모든 구독 기능이 즉시 취소됩니다.\n'
            '처리 기간: 영업일 기준 3-5일\n'
            '환불 방법: 원결제 수단으로 환불\n'
            '특별한 이유 없이는 환불이 불가능합니다.\n',
          ),
        ]),
      ],
    );
  }

  /// 오류 해결 탭
  Widget _buildTroubleshootingTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildFAQSection('앱 관련 문제', [
          _buildFAQItem(
            '앱이 느리거나 멈춰요',
            '해결 방법 (순서대로 시도):\n\n'
            '1. 앱 재시작:\n'
            '• 앱을 완전히 종료 후 재실행\n\n'
            '2. 캐시 정리:\n'
            '• My 페이지 > 설정 > 캐시 정리\n\n'
            '3. 저장 공간 확보:\n'
            '• 디바이스 저장 공간 10% 이상 확보\n\n'
            '4. 앱 업데이트:\n'
            '• 앱스토어/플레이스토어에서 최신 버전 확인\n\n'
            '5. 디바이스 재시작:\n'
            '• 스마트폰/태블릿 재부팅'
          ),
      ],
        ),
      ],
    );
  }

  /// 고객 지원 탭
  Widget _buildSupportTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildFAQSection('연락처 정보', [
          _buildContactItem(
            '이메일 문의',
            '<EMAIL>',
            '기술 지원 및 문의사항',
            Icons.email,
            () => _launchEmail(),
          ),
        ], sectionIcon: Icons.contact_support),
        _buildFAQSection('문의 시 필요 정보', [
          _buildFAQItem(
            '문의할 때 어떤 정보가 필요한가요?',
            '빠른 해결을 위해 다음 정보를 포함해주세요:\n\n'
            '기기 정보:\n'
            '• 기기 모델 (예: iPhone 14, Galaxy S23)\n'
            '• 운영체제 버전 (iOS 17.0, Android 13)\n\n'
            '앱 정보:\n'
            '• 앱 버전 (My 페이지 하단에서 확인)\n'
            '• 구독 플랜 (무료/플러스/프로)\n\n'
            '문제 상황:\n'
            '• 언제 발생했는지\n'
            '• 어떤 작업 중이었는지\n'
            '• 오류 메시지 (있는 경우)\n'
            '• 스크린샷 (가능한 경우)',
          ),
        ], sectionIcon: Icons.info),
        _buildFAQSection('자가 진단', [
          _buildFAQItem(
            '문의 전 먼저 확인해보세요',
            '1. 앱 버전 확인:\n'
            '• 최신 버전인지 앱스토어에서 확인\n\n'
            '2. 네트워크 연결:\n'
            '• Wi-Fi 또는 모바일 데이터 정상 작동\n\n'
            '3. 저장 공간:\n'
            '• 디바이스 저장 공간 충분한지 확인\n\n'
            '4. 권한 설정:\n'
            '• 앱 권한이 모두 허용되어 있는지 확인\n\n'
            '5. 재시작:\n'
            '• 앱 재시작, 필요시 디바이스 재부팅',
          ),
        ]),
      ],
    );
  }

  /// FAQ 섹션 빌더
  Widget _buildFAQSection(String title, List<Widget> items, {IconData? sectionIcon}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Row(
            children: [
              if (sectionIcon != null) ...[
                Icon(sectionIcon, color: AppColors.primarySeed, size: 24),
                const SizedBox(width: 8),
              ],
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
        ...items,
        const SizedBox(height: 24),
      ],
    );
  }

  /// FAQ 아이템 빌더
  Widget _buildFAQItem(String question, String answer) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        title: Text(
          question,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 15,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                answer,
                style: const TextStyle(
                  fontSize: 14,
                  height: 1.6,
                ),
                textAlign: TextAlign.left,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 연락처 아이템 빌더
  Widget _buildContactItem(
    String title,
    String content,
    String subtitle,
    IconData icon,
    VoidCallback? onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon, color: AppColors.primarySeed),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(content, style: const TextStyle(fontSize: 16)),
            Text(subtitle, style: const TextStyle(fontSize: 12, color: Colors.grey)),
          ],
        ),
        onTap: onTap,
        trailing: onTap != null ? const Icon(Icons.arrow_forward_ios, size: 16) : null,
      ),
    );
  }

  /// 이메일 앱 실행
  Future<void> _launchEmail() async {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('이메일 주소가 클립보드에 복사되었습니다: <EMAIL>'),
        backgroundColor: AppColors.primarySeed,
      ),
    );
  }
}
